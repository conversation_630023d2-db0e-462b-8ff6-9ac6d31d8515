import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useChatMessages, flattenMessages, useUpdateMessage } from '@/hooks/chat';
import { useWebSocket } from '@/lib/websocket';
import { MessageItem } from './message-item';
import { MessageInput } from './message-input';
import { cn } from '@/lib/utils';

interface MessageListProps {
  channelId: number;
  hubId: number;
  channelName?: string;
  className?: string;
}

export const MessageList = React.memo<MessageListProps>(({ channelId, hubId, channelName, className }) => {
  const { t, keys } = useTranslations();
  const { subscribeToChannel, unsubscribeFromChannel, typingUsers } = useWebSocket();
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [editingMessage, setEditingMessage] = useState<{ id: number; content: string } | null>(null);
  const [shouldScrollToBottom, setShouldScrollToBottom] = useState(true);

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
  } = useChatMessages(channelId, { enabled: !!channelId });

  const updateMessageMutation = useUpdateMessage();

  // Memoized values to prevent unnecessary re-computations
  const messages = useMemo(() => flattenMessages(data), [data]);
  const channelTypingUsers = useMemo(() => typingUsers[channelId] || [], [typingUsers, channelId]);

  // Subscribe to WebSocket channel when component mounts
  useEffect(() => {
    if (channelId) {
      subscribeToChannel(channelId);
      return () => unsubscribeFromChannel(channelId);
    }
  }, [channelId, subscribeToChannel, unsubscribeFromChannel]);

  // Auto-scroll to bottom when new messages arrive (if user is near bottom)
  useEffect(() => {
    if (shouldScrollToBottom && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages.length, shouldScrollToBottom]);

  // Check if user is near bottom of scroll area
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
    setShouldScrollToBottom(isNearBottom);

    // Load more messages when scrolling to top
    if (scrollTop < 100 && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const handleEditMessage = useCallback((messageId: number, content: string) => {
    setEditingMessage({ id: messageId, content });
  }, []);

  const handleSaveEdit = useCallback(async (newContent: string) => {
    if (!editingMessage) return;

    try {
      await updateMessageMutation.mutateAsync({
        params: { path: { channelId, messageId: editingMessage.id } },
        body: {
          content: newContent,
          attachment_uris: [], // For now, editing doesn't support changing attachments
        },
      });
      setEditingMessage(null);
      // Success toast is handled by the mutation hook
    } catch (error) {
      // Error handling for message update - error toast is handled by the mutation hook
      throw error; // Re-throw to let UnifiedInput know the save failed
    }
  }, [editingMessage, updateMessageMutation, channelId]);

  const handleCancelEdit = useCallback(() => {
    setEditingMessage(null);
  }, []);

  if (isLoading) {
    return (
      <div className={cn("flex items-center justify-center h-full", className)}>
        <div className="flex items-center gap-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="text-sm text-muted-foreground">
            {t(keys.collaborationHubs.chat.loadingMessages)}
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("flex items-center justify-center h-full", className)}>
        <div className="text-center space-y-2">
          <p className="text-sm text-destructive">{t(keys.collaborationHubs.chat.error)}</p>
          <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
            {t(keys.common.retry)}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full max-h-full", className)}>
      {/* Messages Area */}
      <ScrollArea
        ref={scrollAreaRef}
        className="flex-1 mb-4 min-h-0"
        onScrollCapture={handleScroll}
      >
        <div className="space-y-1 p-2 pt-0">
          {/* Load More Button */}
          {hasNextPage && (
            <div className="flex justify-center py-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => fetchNextPage()}
                disabled={isFetchingNextPage}
              >
                {isFetchingNextPage ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    {t(keys.collaborationHubs.chat.loadingMessages)}
                  </>
                ) : (
                  t(keys.collaborationHubs.chat.loadMore)
                )}
              </Button>
            </div>
          )}

          {/* Messages */}
          {messages.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">{t(keys.collaborationHubs.chat.noMessages)}</p>
              <p className="text-sm text-muted-foreground mt-1">
                {t(keys.collaborationHubs.chat.noMessagesDescription)}
              </p>
            </div>
          ) : (
            messages.map((message) => (
              <MessageItem
                key={message.id}
                message={message}
                channelId={channelId}
                onEdit={handleEditMessage}
              />
            ))
          )}

          {/* Typing Indicators */}
          {channelTypingUsers.length > 0 && (
            <div className="flex items-center gap-2 px-2 py-1 text-sm text-muted-foreground">
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
              </div>
              <span>
                {channelTypingUsers.length === 1
                  ? t(keys.collaborationHubs.chat.typing, { name: channelTypingUsers[0].name })
                  : `${channelTypingUsers.length} people are typing...`
                }
              </span>
            </div>
          )}

          {/* Scroll anchor */}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Message Input */}
      <MessageInput
        channelId={channelId}
        hubId={hubId}
        placeholder={
          channelName
            ? t(keys.collaborationHubs.chat.messagePlaceholder, { channelName: channelName.toLowerCase() })
            : undefined
        }
        isEditing={!!editingMessage}
        editingContent={editingMessage?.content || ''}
        onSaveEdit={handleSaveEdit}
        onCancelEdit={handleCancelEdit}
      />
    </div>
  );
});

MessageList.displayName = 'MessageList';
