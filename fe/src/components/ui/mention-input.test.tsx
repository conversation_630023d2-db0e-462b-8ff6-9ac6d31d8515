import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { MentionInput } from './mention-input'

// Mock the hooks and contexts
vi.mock('@/hooks/collaboration-hubs', () => ({
  useHubParticipants: vi.fn(() => ({
    data: {
      content: [
        {
          id: 1,
          name: '<PERSON>',
          email: '<EMAIL>',
          role: 'admin'
        },
        {
          id: 2,
          name: '<PERSON>',
          email: '<EMAIL>',
          role: 'content_creator'
        }
      ]
    },
    isLoading: false
  }))
}))

vi.mock('@/lib/i18n/typed-translations', () => ({
  useTranslations: () => ({
    t: (key: string) => key,
    keys: {
      ui: {
        mentionInput: {
          loadingParticipants: 'Loading participants...'
        }
      },
      collaborationHubs: {
        roles: {
          admin: 'Admin',
          content_creator: 'Content Creator',
          reviewer: 'Reviewer',
          reviewer_creator: 'Reviewer & Creator'
        }
      }
    }
  })
}))

vi.mock('@/contexts/auth-context', () => ({
  useCurrentUser: () => ({
    id: 999,
    email: '<EMAIL>',
    name: 'Current User'
  })
}))

const createTestWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('MentionInput', () => {
  let mockOnChange: ReturnType<typeof vi.fn>
  
  beforeEach(() => {
    mockOnChange = vi.fn()
    // Mock window.getSelection for DOM manipulation tests
    Object.defineProperty(window, 'getSelection', {
      writable: true,
      value: vi.fn(() => ({
        rangeCount: 1,
        getRangeAt: vi.fn(() => ({
          startContainer: document.createTextNode(''),
          startOffset: 0,
          endContainer: document.createTextNode(''),
          endOffset: 0
        })),
        removeAllRanges: vi.fn(),
        addRange: vi.fn()
      }))
    })
  })

  it('should not trigger dropdown when cursor is after a completed mention token', async () => {
    const TestComponent = () => (
      <MentionInput
        hubId={1}
        value="@<EMAIL> test"
        onChange={mockOnChange}
      >
        {(props) => (
          <div
            {...props}
            data-testid="mention-input"
            style={{ minHeight: '40px', border: '1px solid #ccc', padding: '8px' }}
          />
        )}
      </MentionInput>
    )

    const Wrapper = createTestWrapper()
    render(<TestComponent />, { wrapper: Wrapper })
    
    const input = screen.getByTestId('mention-input')
    
    // Simulate the DOM structure after a mention is inserted
    input.innerHTML = '<span class="mention-token" contenteditable="false" data-email="<EMAIL>">@John Doe</span>'
    
    // Create a mock selection that simulates cursor right after the mention token
    const mockRange = {
      startContainer: input,
      startOffset: 1, // Right after the mention span
      endContainer: input,
      endOffset: 1
    }
    
    const mockSelection = {
      rangeCount: 1,
      getRangeAt: () => mockRange,
      removeAllRanges: vi.fn(),
      addRange: vi.fn()
    }
    
    vi.mocked(window.getSelection).mockReturnValue(mockSelection as any)
    
    // Trigger input event to simulate user interaction
    fireEvent.input(input)
    
    // Wait for any async operations
    await waitFor(() => {
      // The dropdown should not appear - we check that no participant items are rendered
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument()
    })
  })

  it('should allow deleting mention tokens with backspace', async () => {
    const TestComponent = () => (
      <MentionInput
        hubId={1}
        value="@<EMAIL> test"
        onChange={mockOnChange}
      >
        {(props) => (
          <div
            {...props}
            data-testid="mention-input"
            style={{ minHeight: '40px', border: '1px solid #ccc', padding: '8px' }}
          />
        )}
      </MentionInput>
    )

    const Wrapper = createTestWrapper()
    render(<TestComponent />, { wrapper: Wrapper })

    const input = screen.getByTestId('mention-input')

    // Simulate the DOM structure with a mention token followed by text
    const mentionSpan = document.createElement('span')
    mentionSpan.className = 'mention-token'
    mentionSpan.contentEditable = 'false'
    mentionSpan.dataset.email = '<EMAIL>'
    mentionSpan.textContent = '@John Doe'

    const textNode = document.createTextNode(' test')

    input.innerHTML = ''
    input.appendChild(mentionSpan)
    input.appendChild(textNode)

    // Create a mock selection at the beginning of the text node (right after mention token)
    const mockRange = {
      startContainer: textNode,
      startOffset: 0, // At the beginning of " test"
      endContainer: textNode,
      endOffset: 0
    }

    const mockSelection = {
      rangeCount: 1,
      getRangeAt: () => mockRange,
      removeAllRanges: vi.fn(),
      addRange: vi.fn()
    }

    vi.mocked(window.getSelection).mockReturnValue(mockSelection as any)

    // Simulate backspace key press
    fireEvent.keyDown(input, { key: 'Backspace' })

    // Verify that onChange was called (mention token should be deleted)
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalled()
    })

    // Verify the mention token was removed from DOM
    expect(input.querySelector('.mention-token')).not.toBeInTheDocument()
  })
})
